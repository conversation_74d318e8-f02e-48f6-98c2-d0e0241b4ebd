import { Router } from 'express';
import { authenticateSSE, AuthenticatedSSERequest } from '../middlewares/sseMiddleware';
import { serviceRegistry } from '../services/ServiceRegistry';
import { Logger } from '../config/logger';

const router = Router();

// Get polling service instance from registry
const pollingService = serviceRegistry.getPollingService();

/**
 * Subscribe to polling updates
 * POST /api/polling/subscribe
 */
router.post('/subscribe', authenticateSSE, (req: AuthenticatedSSERequest, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'User must be authenticated to subscribe to polling'
      });
    }

    const { types = ['*'] } = req.body;

    if (!Array.isArray(types)) {
      return res.status(400).json({
        error: 'Invalid types parameter',
        message: 'types must be an array of strings'
      });
    }

    const subscriptionId = pollingService.subscribe(
      req.user._id,
      req.user.role,
      types
    );

    res.json({
      success: true,
      subscriptionId,
      message: 'Successfully subscribed to polling updates',
      types,
      nextPollTime: pollingService.getNextPollTime(subscriptionId)
    });
  } catch (error) {
    Logger.error('Failed to create polling subscription:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to create polling subscription'
    });
  }
});

/**
 * Unsubscribe from polling updates
 * POST /api/polling/unsubscribe
 */
router.post('/unsubscribe', authenticateSSE, (req: AuthenticatedSSERequest, res) => {
  try {
    const { subscriptionId } = req.body;

    if (!subscriptionId) {
      return res.status(400).json({
        error: 'Missing subscription ID',
        message: 'subscriptionId is required'
      });
    }

    const success = pollingService.unsubscribe(subscriptionId);

    if (success) {
      res.json({
        success: true,
        message: 'Successfully unsubscribed from polling updates'
      });
    } else {
      res.status(404).json({
        error: 'Subscription not found',
        message: 'Invalid subscription ID or subscription already removed'
      });
    }
  } catch (error) {
    Logger.error('Failed to remove polling subscription:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to remove polling subscription'
    });
  }
});

/**
 * Poll for updates
 * GET /api/polling/poll/:subscriptionId
 */
router.get('/poll/:subscriptionId', authenticateSSE, (req: AuthenticatedSSERequest, res) => {
  try {
    const { subscriptionId } = req.params;
    const { lastUpdateId } = req.query;

    if (!subscriptionId) {
      return res.status(400).json({
        error: 'Missing subscription ID',
        message: 'subscriptionId is required'
      });
    }

    const batch = pollingService.poll(subscriptionId, lastUpdateId as string);

    if (batch) {
      res.json({
        success: true,
        data: batch,
        nextPollTime: pollingService.getNextPollTime(subscriptionId)
      });
    } else {
      res.status(404).json({
        error: 'Subscription not found',
        message: 'Invalid subscription ID or subscription inactive'
      });
    }
  } catch (error) {
    Logger.error('Failed to poll for updates:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to poll for updates'
    });
  }
});

/**
 * Add update for specific user
 * POST /api/polling/add-update
 */
router.post('/add-update', authenticateSSE, (req: AuthenticatedSSERequest, res) => {
  try {
    const { userId, type, data, priority = 'medium', expiresIn } = req.body;

    if (!type || !data) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'type and data are required'
      });
    }

    const update = {
      id: `update_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      type,
      data,
      timestamp: new Date(),
      userId,
      priority,
      expiresAt: expiresIn ? new Date(Date.now() + expiresIn * 1000) : undefined
    };

    pollingService.addUpdate(update);

    res.json({
      success: true,
      message: 'Update added successfully',
      updateId: update.id
    });
  } catch (error) {
    Logger.error('Failed to add polling update:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to add update'
    });
  }
});

/**
 * Broadcast update to user type
 * POST /api/polling/broadcast-to-type
 */
router.post('/broadcast-to-type', authenticateSSE, (req: AuthenticatedSSERequest, res) => {
  try {
    const { userType, type, data, priority = 'medium', expiresIn } = req.body;

    if (!userType || !type || !data) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'userType, type, and data are required'
      });
    }

    if (!['student', 'teacher', 'admin'].includes(userType)) {
      return res.status(400).json({
        error: 'Invalid user type',
        message: 'userType must be student, teacher, or admin'
      });
    }

    const update = {
      type,
      data,
      priority,
      expiresAt: expiresIn ? new Date(Date.now() + expiresIn * 1000) : undefined
    };

    pollingService.broadcastToUserType(userType, update);

    res.json({
      success: true,
      message: `Update broadcast to ${userType}s successfully`,
      userType
    });
  } catch (error) {
    Logger.error('Failed to broadcast update to user type:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to broadcast update'
    });
  }
});

/**
 * Broadcast update to room
 * POST /api/polling/broadcast-to-room
 */
router.post('/broadcast-to-room', authenticateSSE, (req: AuthenticatedSSERequest, res) => {
  try {
    const { roomId, type, data, priority = 'medium', expiresIn } = req.body;

    if (!roomId || !type || !data) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'roomId, type, and data are required'
      });
    }

    const update = {
      type,
      data,
      priority,
      expiresAt: expiresIn ? new Date(Date.now() + expiresIn * 1000) : undefined
    };

    pollingService.broadcastToRoom(roomId, update);

    res.json({
      success: true,
      message: `Update broadcast to room ${roomId} successfully`,
      roomId
    });
  } catch (error) {
    Logger.error('Failed to broadcast update to room:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to broadcast update to room'
    });
  }
});

/**
 * Get polling statistics
 * GET /api/polling/stats
 */
router.get('/stats', authenticateSSE, (req: AuthenticatedSSERequest, res) => {
  try {
    const stats = pollingService.getStats();
    const activeSubscriptions = pollingService.getActiveSubscriptions();

    res.json({
      success: true,
      data: {
        stats,
        activeSubscriptions: activeSubscriptions.length,
        subscriptionDetails: activeSubscriptions.map(sub => ({
          id: sub.id,
          userId: sub.userId,
          userType: sub.userType,
          types: sub.types,
          lastPolled: sub.lastPolled,
          nextPollTime: sub.nextPollTime,
          failureCount: sub.failureCount
        }))
      }
    });
  } catch (error) {
    Logger.error('Failed to get polling stats:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to retrieve polling statistics'
    });
  }
});

/**
 * Clear updates for user
 * POST /api/polling/clear-updates
 */
router.post('/clear-updates', authenticateSSE, (req: AuthenticatedSSERequest, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        error: 'Missing user ID',
        message: 'userId is required'
      });
    }

    pollingService.clearUpdatesForUser(userId);

    res.json({
      success: true,
      message: `Updates cleared for user ${userId}`
    });
  } catch (error) {
    Logger.error('Failed to clear user updates:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to clear user updates'
    });
  }
});

/**
 * Health check endpoint
 * GET /api/polling/health
 */
router.get('/health', (req, res) => {
  try {
    const stats = pollingService.getStats();

    res.json({
      status: 'healthy',
      service: 'Polling Service',
      timestamp: new Date(),
      uptime: process.uptime(),
      stats: {
        activeSubscriptions: stats.activeSubscriptions,
        totalUpdates: stats.totalUpdates,
        errorRate: stats.errorRate
      }
    });
  } catch (error) {
    Logger.error('Polling health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      service: 'Polling Service',
      error: 'Health check failed'
    });
  }
});

export default router;
